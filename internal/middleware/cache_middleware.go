package middleware

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
	"go.uber.org/zap"
)

// CacheMiddleware provides cache invalidation functionality
type CacheMiddleware struct {
	cacheManager *activity_cashback.CacheManager
}

// NewCacheMiddleware creates a new cache middleware
func NewCacheMiddleware() *CacheMiddleware {
	return &CacheMiddleware{
		cacheManager: activity_cashback.NewCacheManager(),
	}
}

// InvalidateUserCacheAfterTaskCompletion invalidates user cache after task completion
func (cm *CacheMiddleware) InvalidateUserCacheAfterTaskCompletion() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Process the request first
		c.Next()

		// Only invalidate cache if the request was successful
		if c.Writer.Status() >= 200 && c.Writer.Status() < 300 {
			// Extract user ID from context
			userIDValue := c.Value("userId")
			if userIDValue == nil {
				return
			}

			userIDStr, ok := userIDValue.(string)
			if !ok {
				return
			}

			userID, err := uuid.Parse(userIDStr)
			if err != nil {
				global.GVA_LOG.Error("Invalid user ID in cache middleware", zap.Error(err))
				return
			}

			// Extract task ID from request if available
			taskIDStr := c.Param("taskId")
			if taskIDStr == "" {
				taskIDStr = c.PostForm("taskId")
			}
			if taskIDStr == "" {
				taskIDStr = c.Query("taskId")
			}

			var taskID uuid.UUID
			if taskIDStr != "" {
				taskID, err = uuid.Parse(taskIDStr)
				if err != nil {
					global.GVA_LOG.Error("Invalid task ID in cache middleware", zap.Error(err))
					return
				}
			}

			// Invalidate cache in background
			go func() {
				ctx := context.Background()
				if taskID != uuid.Nil {
					if err := cm.cacheManager.OnTaskCompleted(ctx, userID, taskID); err != nil {
						global.GVA_LOG.Error("Failed to invalidate cache after task completion", 
							zap.String("user_id", userID.String()),
							zap.String("task_id", taskID.String()),
							zap.Error(err))
					}
				} else {
					// If no specific task ID, invalidate all user task cache
					if err := cm.cacheManager.OnTaskProgressUpdated(ctx, userID, uuid.Nil); err != nil {
						global.GVA_LOG.Error("Failed to invalidate user cache", 
							zap.String("user_id", userID.String()),
							zap.Error(err))
					}
				}
			}()
		}
	}
}

// InvalidateUserCacheAfterTierChange invalidates user cache after tier changes
func (cm *CacheMiddleware) InvalidateUserCacheAfterTierChange() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Store original response to check for tier changes
		c.Next()

		// Only invalidate cache if the request was successful
		if c.Writer.Status() >= 200 && c.Writer.Status() < 300 {
			// Extract user ID from context
			userIDValue := c.Value("userId")
			if userIDValue == nil {
				return
			}

			userIDStr, ok := userIDValue.(string)
			if !ok {
				return
			}

			userID, err := uuid.Parse(userIDStr)
			if err != nil {
				global.GVA_LOG.Error("Invalid user ID in cache middleware", zap.Error(err))
				return
			}

			// Invalidate cache in background
			go func() {
				ctx := context.Background()
				// For tier changes, we invalidate all user cache since tier affects many things
				if err := cm.cacheManager.OnUserTierChanged(ctx, userID, 0, 0); err != nil {
					global.GVA_LOG.Error("Failed to invalidate cache after potential tier change", 
						zap.String("user_id", userID.String()),
						zap.Error(err))
				}
			}()
		}
	}
}

// CacheHealthCheck provides a health check endpoint for cache
func (cm *CacheMiddleware) CacheHealthCheck() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := context.Background()
		
		stats, err := cm.cacheManager.GetCacheStats(ctx)
		if err != nil {
			c.JSON(500, gin.H{
				"status": "unhealthy",
				"error":  err.Error(),
			})
			return
		}

		c.JSON(200, stats)
	}
}

// WarmupCache provides an endpoint to warmup cache
func (cm *CacheMiddleware) WarmupCache() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := context.Background()
		
		if err := cm.cacheManager.WarmupCache(ctx); err != nil {
			c.JSON(500, gin.H{
				"status": "error",
				"error":  err.Error(),
			})
			return
		}

		c.JSON(200, gin.H{
			"status":  "success",
			"message": "Cache warmup completed",
		})
	}
}

// InvalidateAllCache provides an endpoint to invalidate all cache (admin only)
func (cm *CacheMiddleware) InvalidateAllCache() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := context.Background()
		
		if err := cm.cacheManager.OnSystemReset(ctx, "manual"); err != nil {
			c.JSON(500, gin.H{
				"status": "error",
				"error":  err.Error(),
			})
			return
		}

		c.JSON(200, gin.H{
			"status":  "success",
			"message": "All cache invalidated",
		})
	}
}

// RegisterCacheRoutes registers cache-related routes
func (cm *CacheMiddleware) RegisterCacheRoutes(router *gin.Engine) {
	cacheGroup := router.Group("/api/cache")
	{
		cacheGroup.GET("/health", cm.CacheHealthCheck())
		cacheGroup.POST("/warmup", cm.WarmupCache())
		cacheGroup.DELETE("/invalidate", cm.InvalidateAllCache())
	}
}
