package cache

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/test"
)

func TestCacheService(t *testing.T) {
	// Setup test environment
	test.SetupTestConfig()
	defer test.CleanupTestConfig()

	// Skip if Redis is not available
	if !test.IsRedisAvailable() {
		t.Skip("Redis not available, skipping cache tests")
	}

	cacheService := NewCacheService()
	ctx := context.Background()

	t.Run("Set and Get", func(t *testing.T) {
		key := "test:key:1"
		value := map[string]interface{}{
			"id":   1,
			"name": "test",
		}

		// Set value
		err := cacheService.Set(ctx, key, value)
		require.NoError(t, err)

		// Get value
		var result map[string]interface{}
		err = cacheService.Get(ctx, key, &result)
		require.NoError(t, err)
		assert.Equal(t, value, result)

		// Clean up
		err = cacheService.Delete(ctx, key)
		require.NoError(t, err)
	})

	t.Run("Get non-existent key", func(t *testing.T) {
		key := "test:nonexistent"
		var result map[string]interface{}
		
		err := cacheService.Get(ctx, key, &result)
		assert.Error(t, err)
		// Should return redis.Nil error
	})

	t.Run("GetOrSet with cache miss", func(t *testing.T) {
		key := "test:getorset:1"
		expectedValue := map[string]string{
			"data": "from_function",
		}

		var result map[string]string
		err := cacheService.GetOrSet(ctx, key, &result, func() (interface{}, error) {
			return expectedValue, nil
		})

		require.NoError(t, err)
		assert.Equal(t, expectedValue, result)

		// Verify it's now in cache
		var cachedResult map[string]string
		err = cacheService.Get(ctx, key, &cachedResult)
		require.NoError(t, err)
		assert.Equal(t, expectedValue, cachedResult)

		// Clean up
		err = cacheService.Delete(ctx, key)
		require.NoError(t, err)
	})

	t.Run("GetOrSet with cache hit", func(t *testing.T) {
		key := "test:getorset:2"
		cachedValue := map[string]string{
			"data": "from_cache",
		}
		functionValue := map[string]string{
			"data": "from_function",
		}

		// Pre-populate cache
		err := cacheService.Set(ctx, key, cachedValue)
		require.NoError(t, err)

		var result map[string]string
		err = cacheService.GetOrSet(ctx, key, &result, func() (interface{}, error) {
			return functionValue, nil
		})

		require.NoError(t, err)
		// Should get cached value, not function value
		assert.Equal(t, cachedValue, result)

		// Clean up
		err = cacheService.Delete(ctx, key)
		require.NoError(t, err)
	})

	t.Run("Delete multiple keys", func(t *testing.T) {
		keys := []string{"test:multi:1", "test:multi:2", "test:multi:3"}
		value := "test_value"

		// Set multiple keys
		for _, key := range keys {
			err := cacheService.Set(ctx, key, value)
			require.NoError(t, err)
		}

		// Delete all keys
		err := cacheService.Delete(ctx, keys...)
		require.NoError(t, err)

		// Verify all keys are deleted
		for _, key := range keys {
			var result string
			err := cacheService.Get(ctx, key, &result)
			assert.Error(t, err) // Should be redis.Nil
		}
	})

	t.Run("Exists", func(t *testing.T) {
		key := "test:exists"
		value := "test_value"

		// Key should not exist initially
		exists, err := cacheService.Exists(ctx, key)
		require.NoError(t, err)
		assert.False(t, exists)

		// Set key
		err = cacheService.Set(ctx, key, value)
		require.NoError(t, err)

		// Key should exist now
		exists, err = cacheService.Exists(ctx, key)
		require.NoError(t, err)
		assert.True(t, exists)

		// Clean up
		err = cacheService.Delete(ctx, key)
		require.NoError(t, err)
	})

	t.Run("TTL and Expire", func(t *testing.T) {
		key := "test:ttl"
		value := "test_value"

		// Set key with TTL
		err := cacheService.Set(ctx, key, value, CacheOptions{TTL: 1 * time.Second})
		require.NoError(t, err)

		// Check TTL
		ttl, err := cacheService.TTL(ctx, key)
		require.NoError(t, err)
		assert.True(t, ttl > 0 && ttl <= 1*time.Second)

		// Update TTL
		err = cacheService.Expire(ctx, key, 2*time.Second)
		require.NoError(t, err)

		// Check updated TTL
		ttl, err = cacheService.TTL(ctx, key)
		require.NoError(t, err)
		assert.True(t, ttl > 1*time.Second && ttl <= 2*time.Second)

		// Clean up
		err = cacheService.Delete(ctx, key)
		require.NoError(t, err)
	})
}

func TestCacheKeys(t *testing.T) {
	keys := NewActivityCashbackCacheKeys()
	userID := uuid.New()
	categoryName := model.TaskCategoryNameDaily

	t.Run("UserTaskListByCategory", func(t *testing.T) {
		key := keys.UserTaskListByCategory(userID, categoryName)
		expected := "activity_cashback:user_task:" + userID.String() + ":daily"
		assert.Equal(t, expected, key)
	})

	t.Run("TasksByCategory", func(t *testing.T) {
		key := keys.TasksByCategory(categoryName)
		expected := "activity_cashback:task_category:daily"
		assert.Equal(t, expected, key)
	})

	t.Run("UserTierInfo", func(t *testing.T) {
		key := keys.UserTierInfo(userID)
		expected := "activity_cashback:user_tier:" + userID.String()
		assert.Equal(t, expected, key)
	})

	t.Run("UserTaskPattern", func(t *testing.T) {
		pattern := keys.UserTaskPattern(userID)
		expected := "activity_cashback:user_task:" + userID.String() + ":*"
		assert.Equal(t, expected, pattern)
	})
}

func TestCacheKeyBuilder(t *testing.T) {
	userID := uuid.New()
	categoryName := model.TaskCategoryNameDaily

	t.Run("Build cache key", func(t *testing.T) {
		key := NewCacheKeyBuilder().
			Add("activity_cashback").
			Add("user_task").
			AddUUID(userID).
			AddEnum(categoryName).
			Build()

		expected := "activity_cashback:user_task:" + userID.String() + ":daily"
		assert.Equal(t, expected, key)
	})

	t.Run("Empty builder", func(t *testing.T) {
		key := NewCacheKeyBuilder().Build()
		assert.Equal(t, "", key)
	})
}
