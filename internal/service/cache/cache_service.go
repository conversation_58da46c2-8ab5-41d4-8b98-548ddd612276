package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"go.uber.org/zap"
	"golang.org/x/sync/singleflight"
)

// CacheService provides caching functionality with Redis thundering herd protection
type CacheService struct {
	client      redis.UniversalClient
	sfGroup     *singleflight.Group
	defaultTTL  time.Duration
	lockTimeout time.Duration
}

// CacheOptions contains options for cache operations
type CacheOptions struct {
	TTL         time.Duration
	LockTimeout time.Duration
}

// NewCacheService creates a new cache service instance
func NewCacheService() *CacheService {
	return &CacheService{
		client:      global.GVA_REDIS,
		sfGroup:     &singleflight.Group{},
		defaultTTL:  5 * time.Minute,
		lockTimeout: 30 * time.Second,
	}
}

// Get retrieves a value from cache with thundering herd protection
func (cs *CacheService) Get(ctx context.Context, key string, dest interface{}) error {
	// Try to get from cache first
	val, err := cs.client.Get(ctx, key).Result()
	if err == nil {
		return json.Unmarshal([]byte(val), dest)
	}

	if err != redis.Nil {
		global.GVA_LOG.Error("Redis get error", zap.String("key", key), zap.Error(err))
		return err
	}

	// Cache miss
	return redis.Nil
}

// Set stores a value in cache
func (cs *CacheService) Set(ctx context.Context, key string, value interface{}, opts ...CacheOptions) error {
	ttl := cs.defaultTTL
	if len(opts) > 0 && opts[0].TTL > 0 {
		ttl = opts[0].TTL
	}

	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %w", err)
	}

	err = cs.client.Set(ctx, key, data, ttl).Err()
	if err != nil {
		global.GVA_LOG.Error("Redis set error", zap.String("key", key), zap.Error(err))
		return err
	}

	return nil
}

// GetOrSet retrieves from cache or executes the provided function with thundering herd protection
func (cs *CacheService) GetOrSet(ctx context.Context, key string, dest interface{}, fn func() (interface{}, error), opts ...CacheOptions) error {
	// Try to get from cache first
	err := cs.Get(ctx, key, dest)
	if err == nil {
		return nil
	}

	if err != redis.Nil {
		// Redis error, but we can still try to execute the function
		global.GVA_LOG.Warn("Redis error, falling back to function execution", zap.String("key", key), zap.Error(err))
	}

	// Use singleflight to prevent thundering herd
	result, err, shared := cs.sfGroup.Do(key, func() (interface{}, error) {
		// Double-check cache in case another goroutine already populated it
		if err := cs.Get(ctx, key, dest); err == nil {
			return dest, nil
		}

		// Execute the function to get fresh data
		data, err := fn()
		if err != nil {
			return nil, err
		}

		// Store in cache
		if setErr := cs.Set(ctx, key, data, opts...); setErr != nil {
			global.GVA_LOG.Error("Failed to set cache", zap.String("key", key), zap.Error(setErr))
			// Don't return error here, we still have the data
		}

		return data, nil
	})

	if err != nil {
		return err
	}

	if shared {
		global.GVA_LOG.Debug("Cache result shared via singleflight", zap.String("key", key))
	}

	// If result is already the destination type, we're done
	if result == dest {
		return nil
	}

	// Otherwise, marshal and unmarshal to ensure proper type conversion
	data, err := json.Marshal(result)
	if err != nil {
		return fmt.Errorf("failed to marshal result: %w", err)
	}

	return json.Unmarshal(data, dest)
}

// Delete removes a key from cache
func (cs *CacheService) Delete(ctx context.Context, keys ...string) error {
	if len(keys) == 0 {
		return nil
	}

	err := cs.client.Del(ctx, keys...).Err()
	if err != nil {
		global.GVA_LOG.Error("Redis delete error", zap.Strings("keys", keys), zap.Error(err))
		return err
	}

	return nil
}

// DeletePattern deletes all keys matching a pattern
func (cs *CacheService) DeletePattern(ctx context.Context, pattern string) error {
	keys, err := cs.client.Keys(ctx, pattern).Result()
	if err != nil {
		global.GVA_LOG.Error("Redis keys error", zap.String("pattern", pattern), zap.Error(err))
		return err
	}

	if len(keys) == 0 {
		return nil
	}

	return cs.Delete(ctx, keys...)
}

// Exists checks if a key exists in cache
func (cs *CacheService) Exists(ctx context.Context, key string) (bool, error) {
	count, err := cs.client.Exists(ctx, key).Result()
	if err != nil {
		global.GVA_LOG.Error("Redis exists error", zap.String("key", key), zap.Error(err))
		return false, err
	}

	return count > 0, nil
}

// TTL returns the time to live for a key
func (cs *CacheService) TTL(ctx context.Context, key string) (time.Duration, error) {
	ttl, err := cs.client.TTL(ctx, key).Result()
	if err != nil {
		global.GVA_LOG.Error("Redis TTL error", zap.String("key", key), zap.Error(err))
		return 0, err
	}

	return ttl, nil
}

// Expire sets a timeout on a key
func (cs *CacheService) Expire(ctx context.Context, key string, expiration time.Duration) error {
	err := cs.client.Expire(ctx, key, expiration).Err()
	if err != nil {
		global.GVA_LOG.Error("Redis expire error", zap.String("key", key), zap.Error(err))
		return err
	}

	return nil
}

// Ping tests the Redis connection
func (cs *CacheService) Ping(ctx context.Context) error {
	_, err := cs.client.Ping(ctx).Result()
	if err != nil {
		global.GVA_LOG.Error("Redis ping error", zap.Error(err))
		return err
	}

	return nil
}
