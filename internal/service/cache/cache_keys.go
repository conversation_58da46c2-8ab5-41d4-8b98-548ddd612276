package cache

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

const (
	// Cache key prefixes
	PrefixActivityCashback = "activity_cashback"
	PrefixUserTask         = "user_task"
	PrefixTaskCategory     = "task_category"
	PrefixUserTier         = "user_tier"
	PrefixTaskCenter       = "task_center"

	// Separator for cache keys
	KeySeparator = ":"
)

// ActivityCashbackCacheKeys provides cache key generation for Activity Cashback system
type ActivityCashbackCacheKeys struct{}

// NewActivityCashbackCacheKeys creates a new instance
func NewActivityCashbackCacheKeys() *ActivityCashbackCacheKeys {
	return &ActivityCashbackCacheKeys{}
}

// UserTaskListByCategory generates cache key for user task list by category
func (k *ActivityCashbackCacheKeys) UserTaskListByCategory(userID uuid.UUID, categoryName model.TaskCategoryName) string {
	return fmt.Sprintf("%s%s%s%s%s%s%s",
		PrefixActivityCashback, KeySeparator,
		PrefixUserTask, KeySeparator,
		userID.String(), KeySeparator,
		string(categoryName))
}

// UserTaskProgress generates cache key for user task progress
func (k *ActivityCashbackCacheKeys) UserTaskProgress(userID uuid.UUID) string {
	return fmt.Sprintf("%s%s%s%s%s",
		PrefixActivityCashback, KeySeparator,
		PrefixUserTask, KeySeparator,
		userID.String())
}

// TasksByCategory generates cache key for tasks by category
func (k *ActivityCashbackCacheKeys) TasksByCategory(categoryName model.TaskCategoryName) string {
	return fmt.Sprintf("%s%s%s%s%s",
		PrefixActivityCashback, KeySeparator,
		PrefixTaskCategory, KeySeparator,
		string(categoryName))
}

// UserTierInfo generates cache key for user tier information
func (k *ActivityCashbackCacheKeys) UserTierInfo(userID uuid.UUID) string {
	return fmt.Sprintf("%s%s%s%s%s",
		PrefixActivityCashback, KeySeparator,
		PrefixUserTier, KeySeparator,
		userID.String())
}

// TaskCenter generates cache key for task center data
func (k *ActivityCashbackCacheKeys) TaskCenter(userID uuid.UUID) string {
	return fmt.Sprintf("%s%s%s%s%s",
		PrefixActivityCashback, KeySeparator,
		PrefixTaskCenter, KeySeparator,
		userID.String())
}

// TaskCategories generates cache key for all task categories
func (k *ActivityCashbackCacheKeys) TaskCategories() string {
	return fmt.Sprintf("%s%s%s",
		PrefixActivityCashback, KeySeparator,
		PrefixTaskCategory)
}

// UserTaskPattern generates pattern for user task cache keys
func (k *ActivityCashbackCacheKeys) UserTaskPattern(userID uuid.UUID) string {
	return fmt.Sprintf("%s%s%s%s%s%s*",
		PrefixActivityCashback, KeySeparator,
		PrefixUserTask, KeySeparator,
		userID.String(), KeySeparator)
}

// UserPattern generates pattern for all user-related cache keys
func (k *ActivityCashbackCacheKeys) UserPattern(userID uuid.UUID) string {
	return fmt.Sprintf("%s%s*%s%s*",
		PrefixActivityCashback, KeySeparator,
		KeySeparator,
		userID.String())
}

// CategoryPattern generates pattern for category-related cache keys
func (k *ActivityCashbackCacheKeys) CategoryPattern(categoryName model.TaskCategoryName) string {
	return fmt.Sprintf("%s%s*%s%s*",
		PrefixActivityCashback, KeySeparator,
		KeySeparator,
		string(categoryName))
}

// AllActivityCashbackPattern generates pattern for all activity cashback cache keys
func (k *ActivityCashbackCacheKeys) AllActivityCashbackPattern() string {
	return fmt.Sprintf("%s%s*",
		PrefixActivityCashback, KeySeparator)
}

// CacheKeyBuilder provides a fluent interface for building cache keys
type CacheKeyBuilder struct {
	parts []string
}

// NewCacheKeyBuilder creates a new cache key builder
func NewCacheKeyBuilder() *CacheKeyBuilder {
	return &CacheKeyBuilder{
		parts: make([]string, 0),
	}
}

// Add adds a part to the cache key
func (b *CacheKeyBuilder) Add(part string) *CacheKeyBuilder {
	b.parts = append(b.parts, part)
	return b
}

// AddUUID adds a UUID to the cache key
func (b *CacheKeyBuilder) AddUUID(id uuid.UUID) *CacheKeyBuilder {
	b.parts = append(b.parts, id.String())
	return b
}

// AddEnum adds an enum value to the cache key
func (b *CacheKeyBuilder) AddEnum(enum fmt.Stringer) *CacheKeyBuilder {
	b.parts = append(b.parts, enum.String())
	return b
}

// Build builds the final cache key
func (b *CacheKeyBuilder) Build() string {
	if len(b.parts) == 0 {
		return ""
	}

	result := b.parts[0]
	for i := 1; i < len(b.parts); i++ {
		result += KeySeparator + b.parts[i]
	}
	return result
}

// CacheInvalidator provides methods for cache invalidation
type CacheInvalidator struct {
	cacheService *CacheService
	keys         *ActivityCashbackCacheKeys
}

// NewCacheInvalidator creates a new cache invalidator
func NewCacheInvalidator(cacheService *CacheService) *CacheInvalidator {
	return &CacheInvalidator{
		cacheService: cacheService,
		keys:         NewActivityCashbackCacheKeys(),
	}
}

// InvalidateUserTasks invalidates all user task related caches
func (ci *CacheInvalidator) InvalidateUserTasks(ctx context.Context, userID uuid.UUID) error {
	pattern := ci.keys.UserTaskPattern(userID)
	return ci.cacheService.DeletePattern(ctx, pattern)
}

// InvalidateUserCache invalidates all user-related caches
func (ci *CacheInvalidator) InvalidateUserCache(ctx context.Context, userID uuid.UUID) error {
	pattern := ci.keys.UserPattern(userID)
	return ci.cacheService.DeletePattern(ctx, pattern)
}

// InvalidateCategoryCache invalidates category-related caches
func (ci *CacheInvalidator) InvalidateCategoryCache(ctx context.Context, categoryName model.TaskCategoryName) error {
	pattern := ci.keys.CategoryPattern(categoryName)
	return ci.cacheService.DeletePattern(ctx, pattern)
}

// InvalidateAllActivityCashbackCache invalidates all activity cashback caches
func (ci *CacheInvalidator) InvalidateAllActivityCashbackCache(ctx context.Context) error {
	pattern := ci.keys.AllActivityCashbackPattern()
	return ci.cacheService.DeletePattern(ctx, pattern)
}
