package activity_cashback

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/cache"
	"go.uber.org/zap"
)

// CachedActivityCashbackService wraps ActivityCashbackService with caching
type CachedActivityCashbackService struct {
	*ActivityCashbackService
	cacheService     *cache.CacheService
	cacheKeys        *cache.ActivityCashbackCacheKeys
	cacheInvalidator *cache.CacheInvalidator
}

// NewCachedActivityCashbackService creates a new cached activity cashback service
func NewCachedActivityCashbackService() *CachedActivityCashbackService {
	baseService := NewActivityCashbackService()
	cacheService := cache.NewCacheService()
	
	return &CachedActivityCashbackService{
		ActivityCashbackService: baseService,
		cacheService:            cacheService,
		cacheKeys:               cache.NewActivityCashbackCacheKeys(),
		cacheInvalidator:        cache.NewCacheInvalidator(cacheService),
	}
}

// GetUserTaskListByCategoryWithDetails retrieves user task progress by category with caching
func (s *CachedActivityCashbackService) GetUserTaskListByCategoryWithDetails(ctx context.Context, userID uuid.UUID, categoryName model.TaskCategoryName) ([]TaskWithProgress, error) {
	// Generate cache key
	cacheKey := s.cacheKeys.UserTaskListByCategory(userID, categoryName)
	
	// Try to get from cache with thundering herd protection
	var result []TaskWithProgress
	err := s.cacheService.GetOrSet(ctx, cacheKey, &result, func() (interface{}, error) {
		global.GVA_LOG.Debug("Cache miss, fetching from database", 
			zap.String("cache_key", cacheKey),
			zap.String("user_id", userID.String()),
			zap.String("category", string(categoryName)))
		
		// Call the original method
		return s.ActivityCashbackService.GetUserTaskListByCategoryWithDetails(ctx, userID, categoryName)
	}, cache.CacheOptions{
		TTL: 5 * time.Minute, // Cache for 5 minutes
	})
	
	if err != nil {
		global.GVA_LOG.Error("Failed to get user task list by category with cache", 
			zap.String("cache_key", cacheKey),
			zap.String("user_id", userID.String()),
			zap.String("category", string(categoryName)),
			zap.Error(err))
		return nil, err
	}
	
	global.GVA_LOG.Debug("Successfully retrieved user task list by category", 
		zap.String("cache_key", cacheKey),
		zap.String("user_id", userID.String()),
		zap.String("category", string(categoryName)),
		zap.Int("task_count", len(result)))
	
	return result, nil
}

// GetTasksByCategory retrieves tasks by category with caching
func (s *CachedActivityCashbackService) GetTasksByCategory(ctx context.Context, categoryName model.TaskCategoryName) ([]model.ActivityTask, error) {
	// Generate cache key
	cacheKey := s.cacheKeys.TasksByCategory(categoryName)
	
	// Try to get from cache with thundering herd protection
	var result []model.ActivityTask
	err := s.cacheService.GetOrSet(ctx, cacheKey, &result, func() (interface{}, error) {
		global.GVA_LOG.Debug("Cache miss, fetching tasks from database", 
			zap.String("cache_key", cacheKey),
			zap.String("category", string(categoryName)))
		
		// Call the original method
		return s.ActivityCashbackService.GetTasksByCategory(ctx, categoryName)
	}, cache.CacheOptions{
		TTL: 10 * time.Minute, // Cache for 10 minutes (tasks change less frequently)
	})
	
	if err != nil {
		global.GVA_LOG.Error("Failed to get tasks by category with cache", 
			zap.String("cache_key", cacheKey),
			zap.String("category", string(categoryName)),
			zap.Error(err))
		return nil, err
	}
	
	global.GVA_LOG.Debug("Successfully retrieved tasks by category", 
		zap.String("cache_key", cacheKey),
		zap.String("category", string(categoryName)),
		zap.Int("task_count", len(result)))
	
	return result, nil
}

// GetUserTierInfo retrieves user tier info with caching
func (s *CachedActivityCashbackService) GetUserTierInfo(ctx context.Context, userID uuid.UUID) (*UserTierInfo, error) {
	// Generate cache key
	cacheKey := s.cacheKeys.UserTierInfo(userID)
	
	// Try to get from cache with thundering herd protection
	var result *UserTierInfo
	err := s.cacheService.GetOrSet(ctx, cacheKey, &result, func() (interface{}, error) {
		global.GVA_LOG.Debug("Cache miss, fetching user tier info from database", 
			zap.String("cache_key", cacheKey),
			zap.String("user_id", userID.String()))
		
		// Call the original method
		return s.ActivityCashbackService.GetUserTierInfo(ctx, userID)
	}, cache.CacheOptions{
		TTL: 3 * time.Minute, // Cache for 3 minutes (tier info changes more frequently)
	})
	
	if err != nil {
		global.GVA_LOG.Error("Failed to get user tier info with cache", 
			zap.String("cache_key", cacheKey),
			zap.String("user_id", userID.String()),
			zap.Error(err))
		return nil, err
	}
	
	global.GVA_LOG.Debug("Successfully retrieved user tier info", 
		zap.String("cache_key", cacheKey),
		zap.String("user_id", userID.String()))
	
	return result, nil
}

// GetTaskCenter retrieves task center data with caching
func (s *CachedActivityCashbackService) GetTaskCenter(ctx context.Context, userID uuid.UUID) (*TaskCenter, error) {
	// Generate cache key
	cacheKey := s.cacheKeys.TaskCenter(userID)
	
	// Try to get from cache with thundering herd protection
	var result *TaskCenter
	err := s.cacheService.GetOrSet(ctx, cacheKey, &result, func() (interface{}, error) {
		global.GVA_LOG.Debug("Cache miss, fetching task center from database", 
			zap.String("cache_key", cacheKey),
			zap.String("user_id", userID.String()))
		
		// Call the original method
		return s.ActivityCashbackService.GetTaskCenter(ctx, userID)
	}, cache.CacheOptions{
		TTL: 2 * time.Minute, // Cache for 2 minutes (task center is dynamic)
	})
	
	if err != nil {
		global.GVA_LOG.Error("Failed to get task center with cache", 
			zap.String("cache_key", cacheKey),
			zap.String("user_id", userID.String()),
			zap.Error(err))
		return nil, err
	}
	
	global.GVA_LOG.Debug("Successfully retrieved task center", 
		zap.String("cache_key", cacheKey),
		zap.String("user_id", userID.String()))
	
	return result, nil
}

// InvalidateUserCache invalidates all cache entries for a specific user
func (s *CachedActivityCashbackService) InvalidateUserCache(ctx context.Context, userID uuid.UUID) error {
	global.GVA_LOG.Info("Invalidating user cache", zap.String("user_id", userID.String()))
	
	err := s.cacheInvalidator.InvalidateUserCache(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to invalidate user cache", 
			zap.String("user_id", userID.String()),
			zap.Error(err))
		return err
	}
	
	global.GVA_LOG.Info("Successfully invalidated user cache", zap.String("user_id", userID.String()))
	return nil
}

// InvalidateUserTaskCache invalidates task-related cache entries for a specific user
func (s *CachedActivityCashbackService) InvalidateUserTaskCache(ctx context.Context, userID uuid.UUID) error {
	global.GVA_LOG.Info("Invalidating user task cache", zap.String("user_id", userID.String()))
	
	err := s.cacheInvalidator.InvalidateUserTasks(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to invalidate user task cache", 
			zap.String("user_id", userID.String()),
			zap.Error(err))
		return err
	}
	
	global.GVA_LOG.Info("Successfully invalidated user task cache", zap.String("user_id", userID.String()))
	return nil
}

// InvalidateCategoryCache invalidates cache entries for a specific category
func (s *CachedActivityCashbackService) InvalidateCategoryCache(ctx context.Context, categoryName model.TaskCategoryName) error {
	global.GVA_LOG.Info("Invalidating category cache", zap.String("category", string(categoryName)))
	
	err := s.cacheInvalidator.InvalidateCategoryCache(ctx, categoryName)
	if err != nil {
		global.GVA_LOG.Error("Failed to invalidate category cache", 
			zap.String("category", string(categoryName)),
			zap.Error(err))
		return err
	}
	
	global.GVA_LOG.Info("Successfully invalidated category cache", zap.String("category", string(categoryName)))
	return nil
}
