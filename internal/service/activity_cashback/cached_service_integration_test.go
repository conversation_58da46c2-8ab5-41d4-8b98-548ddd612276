package activity_cashback

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/test"
)

func TestCachedActivityCashbackServiceIntegration(t *testing.T) {
	// Setup test environment
	test.SetupTestConfig()
	test.SetupTestDB()
	defer test.CleanupTestConfig()

	// Skip if Redis is not available
	if !test.IsRedisAvailable() {
		t.Skip("Redis not available, skipping integration tests")
	}

	// Initialize Redis
	initializer.Redis()
	initializer.RedisList()

	// Create services
	cachedService := NewCachedActivityCashbackService()
	ctx := context.Background()

	// Create test data
	userID := uuid.New()
	categoryName := model.TaskCategoryNameDaily

	t.Run("UserTaskListByCategory with caching", func(t *testing.T) {
		// First call should hit database and cache the result
		start := time.Now()
		result1, err := cachedService.GetUserTaskListByCategoryWithDetails(ctx, userID, categoryName)
		duration1 := time.Since(start)
		require.NoError(t, err)
		assert.NotNil(t, result1)

		// Second call should hit cache and be faster
		start = time.Now()
		result2, err := cachedService.GetUserTaskListByCategoryWithDetails(ctx, userID, categoryName)
		duration2 := time.Since(start)
		require.NoError(t, err)
		assert.NotNil(t, result2)

		// Results should be identical
		assert.Equal(t, len(result1), len(result2))

		// Second call should be significantly faster (cache hit)
		// Note: This might not always be true in test environment, but it's a good indicator
		t.Logf("First call (DB): %v, Second call (Cache): %v", duration1, duration2)
	})

	t.Run("Cache invalidation", func(t *testing.T) {
		// Get initial result and cache it
		result1, err := cachedService.GetUserTaskListByCategoryWithDetails(ctx, userID, categoryName)
		require.NoError(t, err)

		// Invalidate user cache
		err = cachedService.InvalidateUserCache(ctx, userID)
		require.NoError(t, err)

		// Next call should hit database again
		result2, err := cachedService.GetUserTaskListByCategoryWithDetails(ctx, userID, categoryName)
		require.NoError(t, err)
		assert.Equal(t, len(result1), len(result2))
	})

	t.Run("TasksByCategory with caching", func(t *testing.T) {
		// First call
		result1, err := cachedService.GetTasksByCategory(ctx, categoryName)
		require.NoError(t, err)
		assert.NotNil(t, result1)

		// Second call should hit cache
		result2, err := cachedService.GetTasksByCategory(ctx, categoryName)
		require.NoError(t, err)
		assert.Equal(t, len(result1), len(result2))
	})

	t.Run("UserTierInfo with caching", func(t *testing.T) {
		// First call
		result1, err := cachedService.GetUserTierInfo(ctx, userID)
		require.NoError(t, err)
		assert.NotNil(t, result1)

		// Second call should hit cache
		result2, err := cachedService.GetUserTierInfo(ctx, userID)
		require.NoError(t, err)
		assert.Equal(t, result1.CurrentTier, result2.CurrentTier)
		assert.Equal(t, result1.TotalPoints, result2.TotalPoints)
	})

	t.Run("TaskCenter with caching", func(t *testing.T) {
		// First call
		result1, err := cachedService.GetTaskCenter(ctx, userID)
		require.NoError(t, err)
		assert.NotNil(t, result1)

		// Second call should hit cache
		result2, err := cachedService.GetTaskCenter(ctx, userID)
		require.NoError(t, err)
		assert.Equal(t, len(result1.Categories), len(result2.Categories))
	})
}

func TestCacheManagerIntegration(t *testing.T) {
	// Setup test environment
	test.SetupTestConfig()
	defer test.CleanupTestConfig()

	// Skip if Redis is not available
	if !test.IsRedisAvailable() {
		t.Skip("Redis not available, skipping cache manager tests")
	}

	// Initialize Redis
	initializer.Redis()
	initializer.RedisList()

	cacheManager := NewCacheManager()
	ctx := context.Background()

	userID := uuid.New()
	taskID := uuid.New()

	t.Run("Health check", func(t *testing.T) {
		err := cacheManager.HealthCheck(ctx)
		assert.NoError(t, err)
	})

	t.Run("Cache stats", func(t *testing.T) {
		stats, err := cacheManager.GetCacheStats(ctx)
		require.NoError(t, err)
		assert.Equal(t, "healthy", stats["status"])
		assert.Equal(t, true, stats["redis_connected"])
	})

	t.Run("Task completion event", func(t *testing.T) {
		err := cacheManager.OnTaskCompleted(ctx, userID, taskID)
		assert.NoError(t, err)
	})

	t.Run("Task progress update event", func(t *testing.T) {
		err := cacheManager.OnTaskProgressUpdated(ctx, userID, taskID)
		assert.NoError(t, err)
	})

	t.Run("User tier change event", func(t *testing.T) {
		err := cacheManager.OnUserTierChanged(ctx, userID, 1, 2)
		assert.NoError(t, err)
	})

	t.Run("System reset event", func(t *testing.T) {
		err := cacheManager.OnSystemReset(ctx, "daily")
		assert.NoError(t, err)
	})

	t.Run("Cache warmup", func(t *testing.T) {
		err := cacheManager.WarmupCache(ctx)
		assert.NoError(t, err)
	})
}

func TestThunderingHerdProtection(t *testing.T) {
	// Setup test environment
	test.SetupTestConfig()
	test.SetupTestDB()
	defer test.CleanupTestConfig()

	// Skip if Redis is not available
	if !test.IsRedisAvailable() {
		t.Skip("Redis not available, skipping thundering herd tests")
	}

	// Initialize Redis
	initializer.Redis()
	initializer.RedisList()

	cachedService := NewCachedActivityCashbackService()
	ctx := context.Background()

	userID := uuid.New()
	categoryName := model.TaskCategoryNameDaily

	t.Run("Concurrent requests should not cause thundering herd", func(t *testing.T) {
		// Clear any existing cache
		err := cachedService.InvalidateUserCache(ctx, userID)
		require.NoError(t, err)

		// Make multiple concurrent requests
		const numRequests = 10
		results := make(chan []TaskWithProgress, numRequests)
		errors := make(chan error, numRequests)

		for i := 0; i < numRequests; i++ {
			go func() {
				result, err := cachedService.GetUserTaskListByCategoryWithDetails(ctx, userID, categoryName)
				if err != nil {
					errors <- err
				} else {
					results <- result
				}
			}()
		}

		// Collect results
		var allResults [][]TaskWithProgress
		var allErrors []error

		for i := 0; i < numRequests; i++ {
			select {
			case result := <-results:
				allResults = append(allResults, result)
			case err := <-errors:
				allErrors = append(allErrors, err)
			case <-time.After(10 * time.Second):
				t.Fatal("Timeout waiting for concurrent requests")
			}
		}

		// All requests should succeed
		assert.Empty(t, allErrors, "No errors should occur")
		assert.Len(t, allResults, numRequests, "All requests should complete")

		// All results should be identical (same data)
		if len(allResults) > 1 {
			for i := 1; i < len(allResults); i++ {
				assert.Equal(t, len(allResults[0]), len(allResults[i]), 
					"All results should have the same length")
			}
		}
	})
}

func BenchmarkCachedVsUncached(b *testing.B) {
	// Setup test environment
	test.SetupTestConfig()
	test.SetupTestDB()
	defer test.CleanupTestConfig()

	// Skip if Redis is not available
	if !test.IsRedisAvailable() {
		b.Skip("Redis not available, skipping benchmark")
	}

	// Initialize Redis
	initializer.Redis()
	initializer.RedisList()

	cachedService := NewCachedActivityCashbackService()
	uncachedService := NewActivityCashbackService()
	ctx := context.Background()

	userID := uuid.New()
	categoryName := model.TaskCategoryNameDaily

	// Warm up cache
	_, _ = cachedService.GetUserTaskListByCategoryWithDetails(ctx, userID, categoryName)

	b.Run("Uncached", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_, err := uncachedService.GetUserTaskListByCategoryWithDetails(ctx, userID, categoryName)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("Cached", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_, err := cachedService.GetUserTaskListByCategoryWithDetails(ctx, userID, categoryName)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}
