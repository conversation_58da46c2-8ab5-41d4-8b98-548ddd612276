package activity_cashback

import (
	"context"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/cache"
	"go.uber.org/zap"
)

// CacheManager handles cache invalidation for Activity Cashback system
type CacheManager struct {
	cacheService     *cache.CacheService
	cacheInvalidator *cache.CacheInvalidator
}

// NewCacheManager creates a new cache manager
func NewCacheManager() *CacheManager {
	cacheService := cache.NewCacheService()
	return &CacheManager{
		cacheService:     cacheService,
		cacheInvalidator: cache.NewCacheInvalidator(cacheService),
	}
}

// OnTaskCompleted handles cache invalidation when a task is completed
func (cm *CacheManager) OnTaskCompleted(ctx context.Context, userID uuid.UUID, taskID uuid.UUID) error {
	global.GVA_LOG.Info("Invalidating cache after task completion", 
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()))

	// Invalidate user-specific caches
	if err := cm.cacheInvalidator.InvalidateUserCache(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to invalidate user cache after task completion", 
			zap.String("user_id", userID.String()),
			zap.String("task_id", taskID.String()),
			zap.Error(err))
		return err
	}

	return nil
}

// OnTaskProgressUpdated handles cache invalidation when task progress is updated
func (cm *CacheManager) OnTaskProgressUpdated(ctx context.Context, userID uuid.UUID, taskID uuid.UUID) error {
	global.GVA_LOG.Info("Invalidating cache after task progress update", 
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()))

	// Invalidate user task caches
	if err := cm.cacheInvalidator.InvalidateUserTasks(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to invalidate user task cache after progress update", 
			zap.String("user_id", userID.String()),
			zap.String("task_id", taskID.String()),
			zap.Error(err))
		return err
	}

	return nil
}

// OnUserTierChanged handles cache invalidation when user tier changes
func (cm *CacheManager) OnUserTierChanged(ctx context.Context, userID uuid.UUID, oldTier, newTier int) error {
	global.GVA_LOG.Info("Invalidating cache after tier change", 
		zap.String("user_id", userID.String()),
		zap.Int("old_tier", oldTier),
		zap.Int("new_tier", newTier))

	// Invalidate all user caches since tier affects many things
	if err := cm.cacheInvalidator.InvalidateUserCache(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to invalidate user cache after tier change", 
			zap.String("user_id", userID.String()),
			zap.Int("old_tier", oldTier),
			zap.Int("new_tier", newTier),
			zap.Error(err))
		return err
	}

	return nil
}

// OnTaskCreated handles cache invalidation when a new task is created
func (cm *CacheManager) OnTaskCreated(ctx context.Context, task *model.ActivityTask) error {
	global.GVA_LOG.Info("Invalidating cache after task creation", 
		zap.String("task_id", task.ID.String()),
		zap.String("category_id", task.CategoryID.String()))

	// Get category name to invalidate category-specific caches
	// Note: In a real implementation, you might want to fetch the category name
	// For now, we'll invalidate all activity cashback caches
	if err := cm.cacheInvalidator.InvalidateAllActivityCashbackCache(ctx); err != nil {
		global.GVA_LOG.Error("Failed to invalidate cache after task creation", 
			zap.String("task_id", task.ID.String()),
			zap.Error(err))
		return err
	}

	return nil
}

// OnTaskUpdated handles cache invalidation when a task is updated
func (cm *CacheManager) OnTaskUpdated(ctx context.Context, task *model.ActivityTask) error {
	global.GVA_LOG.Info("Invalidating cache after task update", 
		zap.String("task_id", task.ID.String()),
		zap.String("category_id", task.CategoryID.String()))

	// Invalidate all activity cashback caches since task changes affect multiple users
	if err := cm.cacheInvalidator.InvalidateAllActivityCashbackCache(ctx); err != nil {
		global.GVA_LOG.Error("Failed to invalidate cache after task update", 
			zap.String("task_id", task.ID.String()),
			zap.Error(err))
		return err
	}

	return nil
}

// OnTaskDeleted handles cache invalidation when a task is deleted
func (cm *CacheManager) OnTaskDeleted(ctx context.Context, taskID uuid.UUID, categoryID uuid.UUID) error {
	global.GVA_LOG.Info("Invalidating cache after task deletion", 
		zap.String("task_id", taskID.String()),
		zap.String("category_id", categoryID.String()))

	// Invalidate all activity cashback caches
	if err := cm.cacheInvalidator.InvalidateAllActivityCashbackCache(ctx); err != nil {
		global.GVA_LOG.Error("Failed to invalidate cache after task deletion", 
			zap.String("task_id", taskID.String()),
			zap.Error(err))
		return err
	}

	return nil
}

// OnCategoryUpdated handles cache invalidation when a category is updated
func (cm *CacheManager) OnCategoryUpdated(ctx context.Context, categoryName model.TaskCategoryName) error {
	global.GVA_LOG.Info("Invalidating cache after category update", 
		zap.String("category", string(categoryName)))

	// Invalidate category-specific caches
	if err := cm.cacheInvalidator.InvalidateCategoryCache(ctx, categoryName); err != nil {
		global.GVA_LOG.Error("Failed to invalidate category cache after update", 
			zap.String("category", string(categoryName)),
			zap.Error(err))
		return err
	}

	return nil
}

// OnSystemReset handles cache invalidation when system is reset (e.g., daily/weekly/monthly reset)
func (cm *CacheManager) OnSystemReset(ctx context.Context, resetType string) error {
	global.GVA_LOG.Info("Invalidating all cache after system reset", 
		zap.String("reset_type", resetType))

	// Invalidate all activity cashback caches
	if err := cm.cacheInvalidator.InvalidateAllActivityCashbackCache(ctx); err != nil {
		global.GVA_LOG.Error("Failed to invalidate all cache after system reset", 
			zap.String("reset_type", resetType),
			zap.Error(err))
		return err
	}

	return nil
}

// WarmupCache pre-loads frequently accessed data into cache
func (cm *CacheManager) WarmupCache(ctx context.Context) error {
	global.GVA_LOG.Info("Starting cache warmup")

	// This could be implemented to pre-load common data
	// For example:
	// - All task categories
	// - Popular tasks
	// - System configurations
	
	// For now, we'll just log that warmup is complete
	global.GVA_LOG.Info("Cache warmup completed")
	return nil
}

// HealthCheck checks if cache service is healthy
func (cm *CacheManager) HealthCheck(ctx context.Context) error {
	return cm.cacheService.Ping(ctx)
}

// GetCacheStats returns cache statistics (if available)
func (cm *CacheManager) GetCacheStats(ctx context.Context) (map[string]interface{}, error) {
	stats := map[string]interface{}{
		"status": "healthy",
	}

	// Test Redis connection
	if err := cm.cacheService.Ping(ctx); err != nil {
		stats["status"] = "unhealthy"
		stats["error"] = err.Error()
		return stats, err
	}

	// Add more stats if needed
	stats["redis_connected"] = true
	
	return stats, nil
}
