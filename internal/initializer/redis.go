package initializer

import (
	"context"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"go.uber.org/zap"
)

var (
	redisClientInstance redis.UniversalClient
	onceInitRedis       sync.Once
)

// Redis initializes the Redis client
func Redis() {
	onceInitRedis.Do(func() {
		global.GVA_LOG.Info("Initializing Redis client...")

		// Create Redis client
		redisClientInstance = redis.NewClient(&redis.Options{
			Addr:         global.GVA_CONFIG.Redis.Addr,
			Password:     global.GVA_CONFIG.Redis.Password,
			DB:           global.GVA_CONFIG.Redis.DB,
			PoolSize:     10,
			MinIdleConns: 5,
			MaxRetries:   3,
			DialTimeout:  5 * time.Second,
			ReadTimeout:  3 * time.Second,
			WriteTimeout: 3 * time.Second,
		})

		// Test connection
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		_, err := redisClientInstance.Ping(ctx).Result()
		if err != nil {
			global.GVA_LOG.Error("Failed to connect to Redis", zap.Error(err))
			return
		}

		// Set global Redis client
		global.GVA_REDIS = redisClientInstance

		global.GVA_LOG.Info("Redis client initialized successfully")
	})
}

// RedisList initializes multiple Redis clients (if needed in the future)
func RedisList() {
	// For now, we only use one Redis instance
	// This can be extended later if multiple Redis instances are needed
	redisMap := make(map[string]redis.UniversalClient)
	redisMap["default"] = global.GVA_REDIS
	global.GVA_REDISList = redisMap
}

// CloseRedis closes the Redis connection
func CloseRedis() {
	if redisClientInstance != nil {
		global.GVA_LOG.Info("Closing Redis connection...")
		if err := redisClientInstance.Close(); err != nil {
			global.GVA_LOG.Error("Failed to close Redis connection", zap.Error(err))
		} else {
			global.GVA_LOG.Info("Redis connection closed")
		}
	}
}
